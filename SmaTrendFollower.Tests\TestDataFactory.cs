using Alpaca.Markets;
using Moq;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Fast, lightweight test data factory that generates minimal but sufficient test data.
/// Optimized for speed over realism - use only what's needed for each test.
/// </summary>
public static class TestDataFactory
{
    private static readonly Random SharedRandom = new(42); // Fixed seed for reproducible tests

    /// <summary>
    /// Creates a minimal set of bars for testing. Use this instead of generating 250+ bars.
    /// </summary>
    /// <param name="symbol">Symbol name</param>
    /// <param name="count">Number of bars (default: 10 for fast tests)</param>
    /// <param name="basePrice">Starting price</param>
    /// <param name="trend">Price trend: 1.0 = flat, >1.0 = uptrend, <1.0 = downtrend</param>
    /// <returns>Lightweight mock bars</returns>
    public static List<IBar> CreateMinimalBars(string symbol, int count = 10, decimal basePrice = 100m, decimal trend = 1.0m)
    {
        var bars = new List<IBar>(count);
        var baseDate = DateTime.UtcNow.AddDays(-count);

        for (int i = 0; i < count; i++)
        {
            var price = basePrice * (decimal)Math.Pow((double)trend, i / 10.0);
            var bar = Mock.Of<IBar>(b =>
                b.Symbol == symbol &&
                b.Close == price &&
                b.Open == price * 0.999m &&
                b.High == price * 1.001m &&
                b.Low == price * 0.998m &&
                b.Volume == 1000000 &&
                b.TimeUtc == baseDate.AddDays(i) &&
                b.Vwap == 0 &&
                b.TradeCount == 0);
            bars.Add(bar);
        }

        return bars;
    }

    /// <summary>
    /// Creates bars optimized for SMA calculations. Only generates the minimum needed.
    /// </summary>
    /// <param name="symbol">Symbol name</param>
    /// <param name="currentPrice">Current (last) price</param>
    /// <param name="smaValue">Target SMA value</param>
    /// <param name="smaLength">SMA period (e.g., 50, 200)</param>
    /// <returns>Minimal bars for SMA testing</returns>
    public static List<IBar> CreateSmaOptimizedBars(string symbol, decimal currentPrice, decimal smaValue, int smaLength)
    {
        var bars = new List<IBar>(smaLength + 1);
        var baseDate = DateTime.UtcNow.AddDays(-smaLength);

        // Create bars where most are at smaValue, last is at currentPrice
        // This ensures SMA calculation will be close to smaValue
        for (int i = 0; i < smaLength; i++)
        {
            var price = i == smaLength - 1 ? currentPrice : smaValue;
            var bar = Mock.Of<IBar>(b =>
                b.Symbol == symbol &&
                b.Close == price &&
                b.Open == price &&
                b.High == price &&
                b.Low == price &&
                b.Volume == 1000000 &&
                b.TimeUtc == baseDate.AddDays(i));
            bars.Add(bar);
        }

        return bars;
    }

    /// <summary>
    /// Creates a mock IPage<IBar> response for API calls
    /// </summary>
    public static IPage<IBar> CreateMockBarPage(List<IBar> bars)
    {
        return Mock.Of<IPage<IBar>>(p => p.Items == bars);
    }

    /// <summary>
    /// Creates a mock IPage<IBar> response with minimal bars
    /// </summary>
    public static IPage<IBar> CreateMockBarPage(string symbol, int count = 10, decimal basePrice = 100m)
    {
        var bars = CreateMinimalBars(symbol, count, basePrice);
        return CreateMockBarPage(bars);
    }

    /// <summary>
    /// Creates bars with specific volatility for testing ATR calculations
    /// </summary>
    public static List<IBar> CreateVolatilityBars(string symbol, decimal basePrice, decimal atrValue, int count = 14)
    {
        var bars = new List<IBar>(count);
        var baseDate = DateTime.UtcNow.AddDays(-count);

        for (int i = 0; i < count; i++)
        {
            // Create bars with consistent ATR
            var price = basePrice + (decimal)(SharedRandom.NextDouble() - 0.5) * atrValue;
            var high = price + atrValue * 0.5m;
            var low = price - atrValue * 0.5m;

            var bar = Mock.Of<IBar>(b =>
                b.Symbol == symbol &&
                b.Close == price &&
                b.Open == price &&
                b.High == high &&
                b.Low == Math.Max(0.01m, low) &&
                b.Volume == 1000000 &&
                b.TimeUtc == baseDate.AddDays(i));
            bars.Add(bar);
        }

        return bars;
    }

    /// <summary>
    /// Creates bars with a specific return over the period
    /// </summary>
    public static List<IBar> CreateReturnBars(string symbol, decimal startPrice, decimal endPrice, int count = 10)
    {
        var bars = new List<IBar>(count);
        var baseDate = DateTime.UtcNow.AddDays(-count);
        var priceStep = (endPrice - startPrice) / (count - 1);

        for (int i = 0; i < count; i++)
        {
            var price = startPrice + priceStep * i;
            var bar = Mock.Of<IBar>(b =>
                b.Symbol == symbol &&
                b.Close == price &&
                b.Open == price &&
                b.High == price &&
                b.Low == price &&
                b.Volume == 1000000 &&
                b.TimeUtc == baseDate.AddDays(i));
            bars.Add(bar);
        }

        return bars;
    }

    /// <summary>
    /// Creates a mock account with specified equity
    /// </summary>
    public static IAccount CreateMockAccount(decimal equity = 100000m, decimal buyingPower = 200000m)
    {
        return Mock.Of<IAccount>(a =>
            a.Equity == equity &&
            a.BuyingPower == buyingPower &&
            a.Cash == equity * 0.5m &&
            a.PortfolioValue == equity);
    }

    /// <summary>
    /// Creates a mock position
    /// </summary>
    public static IPosition CreateMockPosition(string symbol, decimal quantity, decimal marketValue, decimal entryPrice)
    {
        return Mock.Of<IPosition>(p =>
            p.Symbol == symbol &&
            p.Quantity == quantity &&
            p.MarketValue == marketValue &&
            p.AverageEntryPrice == entryPrice &&
            p.Side == quantity > 0 ? PositionSide.Long : PositionSide.Short);
    }

    /// <summary>
    /// Creates a mock order
    /// </summary>
    public static IOrder CreateMockOrder(string symbol, decimal quantity, decimal price, OrderSide side = OrderSide.Buy, OrderStatus status = OrderStatus.Filled)
    {
        return Mock.Of<IOrder>(o =>
            o.Symbol == symbol &&
            o.Quantity == quantity &&
            o.LimitPrice == price &&
            o.Side == side &&
            o.Status == status &&
            o.OrderId == Guid.NewGuid());
    }

    /// <summary>
    /// Creates multiple symbols with consistent test data
    /// </summary>
    public static Dictionary<string, List<IBar>> CreateMultiSymbolBars(string[] symbols, int barsPerSymbol = 10)
    {
        var result = new Dictionary<string, List<IBar>>();
        var basePrice = 100m;

        foreach (var symbol in symbols)
        {
            // Vary price by symbol for realistic testing
            var symbolPrice = basePrice + (decimal)(symbol.GetHashCode() % 100);
            result[symbol] = CreateMinimalBars(symbol, barsPerSymbol, symbolPrice);
        }

        return result;
    }

    /// <summary>
    /// Creates bars that pass common filters (price > $10, volume > 1M, etc.)
    /// </summary>
    public static List<IBar> CreateFilterPassingBars(string symbol, int count = 10)
    {
        return CreateMinimalBars(symbol, count, basePrice: 50m); // Well above $10 threshold
    }

    /// <summary>
    /// Creates bars that fail common filters
    /// </summary>
    public static List<IBar> CreateFilterFailingBars(string symbol, int count = 10)
    {
        var bars = CreateMinimalBars(symbol, count, basePrice: 5m); // Below $10 threshold
        
        // Modify to have low volume
        foreach (var bar in bars)
        {
            Mock.Get(bar).Setup(b => b.Volume).Returns(500000); // Below 1M threshold
        }

        return bars;
    }
}

/// <summary>
/// Constants for common test values to avoid magic numbers
/// </summary>
public static class TestConstants
{
    public const decimal DefaultPrice = 100m;
    public const decimal DefaultEquity = 100000m;
    public const int MinimalBarCount = 10;
    public const int SmaBarCount = 50; // Minimum for SMA50 tests
    public const int LongSmaBarCount = 200; // Minimum for SMA200 tests
    
    // Common test symbols
    public static readonly string[] TestSymbols = { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
    public static readonly string[] SmallTestSymbols = { "TEST1", "TEST2" }; // For fast tests
}
